"use strict";
const { v4: uuidv4 } = require("uuid");

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Fetch device and nda IDs for association (limited to 2 devices)
    const devices = await queryInterface.sequelize.query(
      "SELECT device_id FROM device LIMIT 2;",
      { type: Sequelize.QueryTypes.SELECT }
    );
    const templates = await queryInterface.sequelize.query(
      "SELECT nda_template_id FROM nda_template LIMIT 2;",
      { type: Sequelize.QueryTypes.SELECT }
    );

    // Prepare device settings for testing kiosk APIs
    const deviceSettings = [];
    for (let i = 0; i < Math.min(devices.length, templates.length); i++) {
      deviceSettings.push({
        device_setting_id: uuidv4(),
        device_id: devices[i].device_id,
        nda_template_id: templates[i].nda_template_id,
        shownda: true, // Enable NDA for testing
        showoutpatient: true, // Enable outpatient for testing
        showexpeditecheckin: true, // Enable expedite check-in for testing
        showwalkinguest: true, // Enable walking guest for testing
        created_at: new Date(),
        updated_at: new Date(),
      });
    }

    if (deviceSettings.length > 0) {
      await queryInterface.bulkInsert("device_setting", deviceSettings, {});
    }
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete("device_setting", null, {});
  },
};
