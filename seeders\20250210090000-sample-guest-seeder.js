"use strict";
const { v4: uuidv4 } = require("uuid");
const { faker } = require("@faker-js/faker");
const logger = require("../config/logger");

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Get existing data for associations
      const patients = await queryInterface.sequelize.query(
        "SELECT patient_id, first_name, last_name, phone FROM patient LIMIT 10;",
        { type: Sequelize.QueryTypes.SELECT, transaction }
      );

      const appointments = await queryInterface.sequelize.query(
        "SELECT appointment_id, patient_id, facility_id FROM appointment LIMIT 10;",
        { type: Sequelize.QueryTypes.SELECT, transaction }
      );

      const facilities = await queryInterface.sequelize.query(
        "SELECT facility_id FROM facility LIMIT 2;",
        { type: Sequelize.QueryTypes.SELECT, transaction }
      );

      if (patients.length === 0 || appointments.length === 0 || facilities.length === 0) {
        logger.warn("Insufficient data for guest seeder. Skipping guest creation.");
        await transaction.commit();
        return;
      }

      // Create patient guests with specific test data for kiosk APIs
      const patientGuests = [];
      const appointmentGuests = [];

      // Create test guests for different scenarios
      const testScenarios = [
        {
          first_name: "John",
          last_name: "Doe",
          email: "<EMAIL>",
          phone: "+**********", // Last 4 digits: 7890
          guest_type: 1, // Friend Family
          relationship_type: 0, // Spouse
          guest_pin: "123456",
          scenario: "Standard guest with PIN for fetch testing"
        },
        {
          first_name: "Jane",
          last_name: "Smith",
          email: "<EMAIL>",
          phone: "+**********", // Last 4 digits: 4321
          guest_type: 0, // Guest
          relationship_type: 1, // Child
          guest_pin: "654321",
          scenario: "Child guest for relationship testing"
        },
        {
          first_name: "Bob",
          last_name: "Wilson",
          email: "<EMAIL>",
          phone: "+**********", // Last 4 digits: 3456
          guest_type: 1, // Friend Family
          relationship_type: 3, // Emergency Contact
          guest_pin: "789012",
          scenario: "Emergency contact for testing"
        },
        {
          first_name: "Alice",
          last_name: "Johnson",
          email: "<EMAIL>",
          phone: "+1444987654", // Last 4 digits: 7654
          guest_type: 0, // Guest
          relationship_type: 2, // Friend
          guest_pin: "345678",
          scenario: "Friend guest for testing"
        },
        {
          first_name: "Mike",
          last_name: "Brown",
          email: "<EMAIL>",
          phone: "+**********", // Last 4 digits: 5777
          guest_type: 1, // Friend Family
          relationship_type: 0, // Spouse
          guest_pin: "901234",
          scenario: "Additional spouse for testing multiple guests"
        }
      ];

      // Create patient guests and appointment guests
      for (let i = 0; i < Math.min(testScenarios.length, patients.length, appointments.length); i++) {
        const scenario = testScenarios[i];
        const patient = patients[i];
        const appointment = appointments[i];
        const facility = facilities[i % facilities.length];

        const patientGuestId = uuidv4();
        const appointmentGuestId = uuidv4();

        // Create patient guest
        patientGuests.push({
          patient_guest_id: patientGuestId,
          patient_id: patient.patient_id,
          first_name: scenario.first_name,
          last_name: scenario.last_name,
          email: scenario.email,
          phone: scenario.phone,
          organization: faker.company.name(),
          guest_type: scenario.guest_type,
          relationship_type: scenario.relationship_type,
          is_emergency_contact: scenario.relationship_type === 3,
          emergency_contact_priority: scenario.relationship_type === 3 ? 1 : null,
          can_make_decisions: scenario.relationship_type === 0 || scenario.relationship_type === 3,
          has_custody: scenario.relationship_type === 1,
          lives_with_patient: scenario.relationship_type === 0 || scenario.relationship_type === 1,
          relationship_notes: `Test ${scenario.scenario}`,
          effective_from: new Date(),
          effective_to: null,
          reason: scenario.guest_type === 2 ? "Test denial reason" : null,
          denied_on: scenario.guest_type === 2 ? new Date() : null,
          image: null,
          is_walkin: false,
          friends_and_family: scenario.guest_type === 1,
          birth_date: faker.date.past({ years: 30, refDate: new Date(2000, 0, 1) }),
          created_at: new Date(),
          updated_at: new Date(),
        });

        // Create appointment guest
        appointmentGuests.push({
          appointment_guest_id: appointmentGuestId,
          appointment_id: appointment.appointment_id,
          patient_guest_id: patientGuestId,
          start_date: new Date(),
          start_time: "09:00:00",
          duration: 60,
          escort_name: null,
          facility_id: facility.facility_id,
          screening: null,
          guest_pin: scenario.guest_pin,
          status: 4, // Registered status for testing
          arrival_time: null,
          departure_time: null,
          created_at: new Date(),
          updated_at: new Date(),
        });
      }

      // Insert patient guests
      if (patientGuests.length > 0) {
        await queryInterface.bulkInsert("patient_guest", patientGuests, { transaction });
        logger.info(`Created ${patientGuests.length} patient guests for kiosk testing`);
      }

      // Insert appointment guests
      if (appointmentGuests.length > 0) {
        await queryInterface.bulkInsert("appointment_guest", appointmentGuests, { transaction });
        logger.info(`Created ${appointmentGuests.length} appointment guests for kiosk testing`);
      }

      // Create additional test patients for outpatient and inpatient scenarios
      const additionalPatients = [];
      const additionalAppointments = [];

      // Outpatient test patient
      const outpatientId = uuidv4();
      additionalPatients.push({
        patient_id: outpatientId,
        first_name: "Sarah",
        last_name: "Connor",
        birth_date: new Date("1990-05-15"),
        phone: "+**********", // For outpatient testing
        email: "<EMAIL>",
        gender: 1,
        marital_status: 0,
        preferred_language: 0,
        created_at: new Date(),
        updated_at: new Date(),
      });

      // Outpatient appointment
      const outpatientAppointmentId = uuidv4();
      additionalAppointments.push({
        appointment_id: outpatientAppointmentId,
        hl7_appointment_id: Math.floor(Math.random() * 90000) + 10000, // Random 5-digit number
        patient_id: outpatientId,
        appointment_date: new Date(),
        type: 0, // Outpatient
        status: 0, // Active
        facility_id: facilities[0].facility_id,
        provider_name: "Dr. Outpatient",
        department: "Outpatient Services",
        created_at: new Date(),
        updated_at: new Date(),
      });

      // Inpatient test patient
      const inpatientId = uuidv4();
      additionalPatients.push({
        patient_id: inpatientId,
        first_name: "John",
        last_name: "Smith",
        birth_date: new Date("1985-03-20"),
        phone: "+**********", // Last 4: 1234 for inpatient testing
        email: "<EMAIL>",
        gender: 0,
        marital_status: 1,
        preferred_language: 0,
        created_at: new Date(),
        updated_at: new Date(),
      });

      // Inpatient appointment
      const inpatientAppointmentId = uuidv4();
      additionalAppointments.push({
        appointment_id: inpatientAppointmentId,
        hl7_appointment_id: Math.floor(Math.random() * 90000) + 10000, // Random 5-digit number
        patient_id: inpatientId,
        appointment_date: new Date(),
        type: 1, // Inpatient
        status: 0, // Active
        facility_id: facilities[0].facility_id,
        provider_name: "Dr. Inpatient",
        department: "Inpatient Services",
        created_at: new Date(),
        updated_at: new Date(),
      });

      // Insert additional test data
      if (additionalPatients.length > 0) {
        await queryInterface.bulkInsert("patient", additionalPatients, { transaction });
        logger.info(`Created ${additionalPatients.length} additional test patients`);
      }

      if (additionalAppointments.length > 0) {
        await queryInterface.bulkInsert("appointment", additionalAppointments, { transaction });
        logger.info(`Created ${additionalAppointments.length} additional test appointments`);
      }

      await transaction.commit();
      logger.info("Guest seeder completed successfully");
    } catch (error) {
      logger.error("Guest seeding error:", error);
      await transaction.rollback();
      throw error;
    }
  },

  down: async (queryInterface) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Delete in reverse order to maintain referential integrity
      await queryInterface.bulkDelete("appointment_guest", null, { transaction });
      await queryInterface.bulkDelete("patient_guest", null, { transaction });

      // Delete additional test appointments and patients created by this seeder
      await queryInterface.sequelize.query(
        "DELETE FROM appointment WHERE provider_name IN ('Dr. Outpatient', 'Dr. Inpatient')",
        { transaction }
      );
      await queryInterface.sequelize.query(
        "DELETE FROM patient WHERE email IN ('<EMAIL>', '<EMAIL>')",
        { transaction }
      );

      await transaction.commit();
      logger.info("Guest seeder rollback completed successfully");
    } catch (error) {
      logger.error("Guest seeder rollback error:", error);
      await transaction.rollback();
      throw error;
    }
  },
};
